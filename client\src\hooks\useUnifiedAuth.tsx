import { useSupabaseAuth } from "./useSupabaseAuth";
import { useMockAuth } from "./useMockAuth";

// Unified auth hook that works with both Supabase and Mock auth
export function useUnifiedAuth() {
  // Check if we should use mock auth
  const useMockAuth = window.location.search.includes('mock=true') || 
                     window.location.pathname.includes('simple-login');

  const supabaseAuth = useSupabaseAuth();
  const mockAuth = useMockAuth();

  // Return the appropriate auth system
  if (useMockAuth) {
    return mockAuth;
  } else {
    return supabaseAuth;
  }
}
