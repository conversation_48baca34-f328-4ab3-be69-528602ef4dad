<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediKey Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 MediKey Application Test</h1>
        <p>Testing the MediKey application running on localhost:5000</p>
        
        <div class="status info">
            <strong>Server Status:</strong> <span id="serverStatus">Checking...</span>
        </div>
        
        <div class="status info">
            <strong>API Status:</strong> <span id="apiStatus">Checking...</span>
        </div>
        
        <div class="status info">
            <strong>Authentication:</strong> <span id="authStatus">Not tested</span>
        </div>
        
        <h3>Test Actions:</h3>
        <button onclick="testServer()">Test Server</button>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testHealthMetrics()">Test Health Metrics</button>
        <button onclick="openApp()">Open MediKey App</button>
        
        <div id="results"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testServer() {
            try {
                const response = await fetch('http://localhost:5000/');
                if (response.ok) {
                    document.getElementById('serverStatus').textContent = '✅ Running';
                    log('Server is running successfully', 'success');
                } else {
                    document.getElementById('serverStatus').textContent = '❌ Error';
                    log(`Server error: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('serverStatus').textContent = '❌ Offline';
                log(`Server connection failed: ${error.message}`, 'error');
            }
        }

        async function testAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('apiStatus').textContent = '✅ Working';
                    log(`API health check: ${JSON.stringify(data)}`, 'success');
                } else {
                    document.getElementById('apiStatus').textContent = '❌ Error';
                    log(`API error: ${response.status}`, 'error');
                }
            } catch (error) {
                document.getElementById('apiStatus').textContent = '❌ Failed';
                log(`API connection failed: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'manasvi',
                        password: 'password123'
                    }),
                    credentials: 'include'
                });
                
                const data = await response.json();
                if (response.ok) {
                    document.getElementById('authStatus').textContent = '✅ Working';
                    log(`Login successful: ${JSON.stringify(data)}`, 'success');
                } else {
                    document.getElementById('authStatus').textContent = '❌ Failed';
                    log(`Login failed: ${data.message}`, 'error');
                }
            } catch (error) {
                document.getElementById('authStatus').textContent = '❌ Error';
                log(`Login error: ${error.message}`, 'error');
            }
        }

        async function testHealthMetrics() {
            try {
                // First login to get session
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'manasvi', password: 'password123' }),
                    credentials: 'include'
                });

                if (loginResponse.ok) {
                    // Then test health metrics
                    const metricsResponse = await fetch('http://localhost:5000/api/health-metrics', {
                        credentials: 'include'
                    });
                    
                    if (metricsResponse.ok) {
                        const data = await metricsResponse.json();
                        log('Health metrics loaded successfully', 'success');
                        log(`Blood pressure: ${data.bloodPressure?.latest}`, 'info');
                        log(`Blood sugar: ${data.bloodSugar?.latest}`, 'info');
                    } else {
                        log(`Health metrics failed: ${metricsResponse.status}`, 'error');
                    }
                } else {
                    log('Login required for health metrics test', 'error');
                }
            } catch (error) {
                log(`Health metrics error: ${error.message}`, 'error');
            }
        }

        function openApp() {
            window.open('http://localhost:5000/', '_blank');
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testServer();
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
