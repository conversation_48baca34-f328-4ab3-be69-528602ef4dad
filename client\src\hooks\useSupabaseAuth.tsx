import { createContext, useContext, useEffect, useState } from "react";
import { useLocation } from "wouter";
import { supabase, authHelpers } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";
import type { User, Session } from "@supabase/supabase-js";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signUp: (email: string, password: string, userData?: any) => Promise<{ data: any; error: any }>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ data: any; error: any }>;
  updatePassword: (password: string) => Promise<{ data: any; error: any }>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  isAuthenticated: false,
  isLoading: true,
  signUp: async () => ({ data: null, error: null }),
  signIn: async () => ({ data: null, error: null }),
  signOut: async () => {},
  resetPassword: async () => ({ data: null, error: null }),
  updatePassword: async () => ({ data: null, error: null }),
});

export function SupabaseAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, navigate] = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        console.log('Starting initial session check...');
        const { session, error } = await authHelpers.getCurrentSession();
        console.log('Initial session check result:', { session, error });

        if (error) {
          console.warn('Session check error (non-fatal):', error);
        }

        setSession(session);
        setUser(session?.user ?? null);
      } catch (error) {
        console.error('Error getting initial session:', error);
        // Don't fail completely, just set no session
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    // Add a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('Session check timeout, setting loading to false');
      setIsLoading(false);
    }, 5000);

    getInitialSession().finally(() => {
      clearTimeout(timeoutId);
    });

    // Listen for auth changes
    let subscription: any = null;

    try {
      const { data } = authHelpers.onAuthStateChange(
        async (event, session) => {
          console.log('Auth state change:', event, session?.user?.email);
          setSession(session);
          setUser(session?.user ?? null);
          setIsLoading(false);

          if (event === 'SIGNED_IN') {
            toast({
              title: "Welcome back!",
              description: "You've successfully signed in.",
            });
            // Only navigate if we're not already on a protected route
            const currentPath = window.location.pathname;
            if (currentPath === '/supabase-login' || currentPath === '/supabase-register' || currentPath === '/') {
              navigate("/dashboard");
            }
          } else if (event === 'SIGNED_OUT') {
            toast({
              title: "Signed out",
              description: "You've been successfully signed out.",
            });
            navigate("/supabase-login");
          } else if (event === 'USER_UPDATED') {
            toast({
              title: "Profile updated",
              description: "Your profile has been updated successfully.",
            });
          }
        }
      );
      subscription = data.subscription;
    } catch (error) {
      console.error('Error setting up auth listener:', error);
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [navigate, toast]);

  const signUp = async (email: string, password: string, userData?: any) => {
    setIsLoading(true);
    const { data, error } = await authHelpers.signUp(email, password, userData);
    
    if (error) {
      toast({
        title: "Sign up failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Sign up successful",
        description: "Please check your email to verify your account.",
      });
    }
    
    setIsLoading(false);
    return { data, error };
  };

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    const { data, error } = await authHelpers.signIn(email, password);

    if (error) {
      toast({
        title: "Sign in failed",
        description: error.message,
        variant: "destructive",
      });
    } else if (data.user) {
      // Manual navigation after successful sign in
      navigate("/dashboard");
    }

    setIsLoading(false);
    return { data, error };
  };

  const signOut = async () => {
    setIsLoading(true);
    const { error } = await authHelpers.signOut();
    
    if (error) {
      toast({
        title: "Sign out failed",
        description: error.message,
        variant: "destructive",
      });
    }
    
    setIsLoading(false);
  };

  const resetPassword = async (email: string) => {
    const { data, error } = await authHelpers.resetPassword(email);
    
    if (error) {
      toast({
        title: "Password reset failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Password reset email sent",
        description: "Please check your email for password reset instructions.",
      });
    }
    
    return { data, error };
  };

  const updatePassword = async (password: string) => {
    const { data, error } = await authHelpers.updatePassword(password);
    
    if (error) {
      toast({
        title: "Password update failed",
        description: error.message,
        variant: "destructive",
      });
    } else {
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      });
    }
    
    return { data, error };
  };

  const contextValue = {
    user,
    session,
    isAuthenticated: !!user,
    isLoading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export const useSupabaseAuth = () => useContext(AuthContext);
