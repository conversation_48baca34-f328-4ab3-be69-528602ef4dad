import { useState } from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { MediKeyLogo } from "@/assets/icons/MediKeyLogo";
import { motion } from "framer-motion";
import { AnimatedContainer, AnimatedItem, AnimatedIcon } from "@/components/ui/animated-container";
import { AlertCircle, Loader2 } from "lucide-react";
import { MoleculeAnimation } from "@/components/ui/molecule-animation";

const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function SupabaseLogin() {
  const [, navigate] = useLocation();
  const { signIn, isLoading, resetPassword } = useSupabaseAuth();
  const [error, setError] = useState<string | null>(null);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setError(null);
    const { error } = await signIn(data.email, data.password);
    
    if (error) {
      setError(error.message);
    }
  };

  const handleForgotPassword = async () => {
    const email = getValues("email");
    if (!email) {
      setError("Please enter your email address first");
      return;
    }
    
    const { error } = await resetPassword(email);
    if (!error) {
      setShowForgotPassword(false);
    }
  };

  return (
    <AnimatedContainer className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-bg-primary px-4 py-12 sm:px-6 lg:px-8">
      <MoleculeAnimation color="#3b82f6" nodeCount={25} opacity={0.15} />
      <div className="w-full max-w-md">
        <AnimatedItem className="text-center mb-8" delay={0.1}>
          <motion.div
            className="flex items-center justify-center"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 15,
              delay: 0.2
            }}
          >
            <AnimatedIcon>
              <MediKeyLogo className="h-12 w-12 text-primary-600" />
            </AnimatedIcon>
            <motion.h1
              className="ml-3 text-3xl font-bold text-gray-800"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              MediKey
            </motion.h1>
          </motion.div>
          <motion.p
            className="mt-2 text-gray-600"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            Universal Digital Medical Record System
          </motion.p>
        </AnimatedItem>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{
            delay: 0.5,
            type: "spring",
            stiffness: 200,
            damping: 20
          }}
        >
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-center">Sign in to your account</CardTitle>
              <CardDescription className="text-center">
                Access your medical records securely with Supabase Auth
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                >
                  <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                </motion.div>
              )}
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-4">
                  <motion.div
                    className="space-y-2"
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      {...register("email")}
                    />
                    {errors.email && (
                      <motion.p
                        className="text-sm text-red-500"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        {errors.email.message}
                      </motion.p>
                    )}
                  </motion.div>
                  <motion.div
                    className="space-y-2"
                    initial={{ x: -10, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.7 }}
                  >
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Password</Label>
                      <Button 
                        type="button"
                        variant="link" 
                        className="p-0 h-auto text-xs"
                        onClick={handleForgotPassword}
                      >
                        Forgot password?
                      </Button>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      {...register("password")}
                    />
                    {errors.password && (
                      <motion.p
                        className="text-sm text-red-500"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        {errors.password.message}
                      </motion.p>
                    )}
                  </motion.div>
                  <motion.div
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.8 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      type="submit"
                      className="w-full"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Signing in...
                        </>
                      ) : (
                        "Sign in"
                      )}
                    </Button>
                  </motion.div>
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-center">
              <motion.p
                className="text-sm text-gray-600"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9 }}
              >
                Don't have an account?{" "}
                <motion.div
                  display="inline-block"
                  whileHover={{ scale: 1.05 }}
                >
                  <Button variant="link" className="p-0 h-auto" onClick={() => navigate("/supabase-register")}>
                    Sign up
                  </Button>
                </motion.div>
              </motion.p>
            </CardFooter>
          </Card>
        </motion.div>

        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <p className="text-xs text-gray-500">
            By signing in, you agree to our{" "}
            <motion.a
              href="#"
              className="text-primary-600 hover:text-primary-800"
              whileHover={{ scale: 1.05 }}
            >
              Terms of Service
            </motion.a>{" "}
            and{" "}
            <motion.a
              href="#"
              className="text-primary-600 hover:text-primary-800"
              whileHover={{ scale: 1.05 }}
            >
              Privacy Policy
            </motion.a>
          </p>
        </motion.div>
      </div>
    </AnimatedContainer>
  );
}
