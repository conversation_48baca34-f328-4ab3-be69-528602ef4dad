import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useLocation } from "wouter";

export default function AuthDebug() {
  const { user, session, isAuthenticated, isLoading, signOut } = useSupabaseAuth();
  const [, navigate] = useLocation();

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Debug</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Loading:</strong> {isLoading ? "Yes" : "No"}
            </div>
            <div>
              <strong>Authenticated:</strong> {isAuthenticated ? "Yes" : "No"}
            </div>
            <div>
              <strong>User:</strong>
              <pre className="bg-gray-100 p-2 rounded mt-2 text-sm overflow-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
            <div>
              <strong>Session:</strong>
              <pre className="bg-gray-100 p-2 rounded mt-2 text-sm overflow-auto">
                {JSON.stringify(session, null, 2)}
              </pre>
            </div>
            <div className="flex gap-4">
              <Button onClick={() => navigate("/supabase-login")}>
                Go to Login
              </Button>
              <Button onClick={() => navigate("/supabase-register")}>
                Go to Register
              </Button>
              <Button onClick={() => navigate("/dashboard")}>
                Go to Dashboard
              </Button>
              {isAuthenticated && (
                <Button onClick={signOut} variant="destructive">
                  Sign Out
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
