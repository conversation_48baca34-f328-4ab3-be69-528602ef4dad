import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { SupabaseAuthProvider } from "@/hooks/useSupabaseAuth";
import { MockAuthProvider } from "@/hooks/useMockAuth";

// Check if we should use mock auth (for testing)
const useMockAuth = window.location.search.includes('mock=true') ||
                   window.location.pathname.includes('simple-login');

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    {useMockAuth ? (
      <MockAuthProvider>
        <App />
        <Toaster />
      </MockAuthProvider>
    ) : (
      <SupabaseAuthProvider>
        <App />
        <Toaster />
      </SupabaseAuthProvider>
    )}
  </QueryClientProvider>
);
