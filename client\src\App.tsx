import { Route, Switch, useLocation } from "wouter";
import { lazy, Suspense, useEffect } from "react";
import Login from "@/pages/login";
import Register from "@/pages/register";
import SupabaseLogin from "@/pages/supabase-login";
import SupabaseRegister from "@/pages/supabase-register";
import SimpleLogin from "@/pages/simple-login";
import ProjectShowcase from "@/pages/project-showcase";
import AuthDebug from "@/pages/auth-debug";
import SupabaseTest from "@/pages/supabase-test";
import NotFound from "@/pages/not-found";
import Sidebar from "@/components/layout/Sidebar";
import MobileMenu from "@/components/layout/MobileMenu";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useMockAuth } from "@/hooks/useMockAuth";
import { getPageTheme, applyTheme } from "@/lib/page-themes";
import { ThemeIndicator } from "@/components/ui/theme-indicator";
import { AnimatedBackground } from "@/components/ui/animated-background";
import { ThemeProvider } from "@/hooks/useTheme";
import { ThemeToggle } from "@/components/ui/theme-toggle";

// Lazy load the mobile access page
const MobileAccess = lazy(() => import("@/pages/mobile-access"));

// Lazy load pages for better performance
const Dashboard = lazy(() => import("@/pages/dashboard"));
const DemoDashboard = lazy(() => import("@/pages/demo-dashboard"));
const Records = lazy(() => import("@/pages/records"));
const Analytics = lazy(() => import("@/pages/analytics"));
const Family = lazy(() => import("@/pages/family"));
const Appointments = lazy(() => import("@/pages/appointments"));
const Assistant = lazy(() => import("@/pages/assistant"));
const Emergency = lazy(() => import("@/pages/emergency"));
const Profile = lazy(() => import("@/pages/profile"));

function LoadingFallback() {
  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <div className="h-16 w-16 animate-pulse rounded-full bg-primary/50"></div>
        <p className="text-lg font-medium text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}

function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const [location] = useLocation();

  // Apply theme based on current page
  useEffect(() => {
    const theme = getPageTheme(location);
    applyTheme(theme);
  }, [location]);

  return (
    <div className="flex h-screen overflow-hidden">
      <AnimatedBackground />
      <Sidebar />
      <MobileMenu />
      <ThemeIndicator />
      <ThemeToggle />
      <main className="flex-1 overflow-y-auto pt-16 md:pt-0 pb-6">
        <Suspense fallback={<LoadingFallback />}>{children}</Suspense>
      </main>
    </div>
  );
}

function App() {
  // Check if we should use mock auth
  const useMockAuthSystem = window.location.search.includes('mock=true') ||
                           window.location.pathname.includes('simple-login');

  const supabaseAuth = useSupabaseAuth();
  const mockAuth = useMockAuth();

  // Use the appropriate auth system
  const { isAuthenticated, isLoading } = useMockAuthSystem ? mockAuth : supabaseAuth;

  // Check if we're on GitHub Pages
  const isGitHubPages = window.location.hostname.includes('github.io');
  const basePath = isGitHubPages ? '/medikey' : '';

  if (isLoading) {
    return <LoadingFallback />;
  }

  return (
    <ThemeProvider>
      <Switch>
        {/* Public routes */}
        <Route path={`${basePath}/login`} component={Login} />
        <Route path={`${basePath}/register`} component={Register} />
        <Route path={`${basePath}/simple-login`} component={SimpleLogin} />
        <Route path={`${basePath}/supabase-login`} component={SupabaseLogin} />
        <Route path={`${basePath}/supabase-register`} component={SupabaseRegister} />
        <Route path={`${basePath}/auth-debug`} component={AuthDebug} />
        <Route path={`${basePath}/supabase-test`} component={SupabaseTest} />
        <Route path={`${basePath}/demo-dashboard`} component={DemoDashboard} />
        <Route path={`${basePath}/showcase`} component={ProjectShowcase} />

        {/* Protected routes */}
        {isAuthenticated ? (
          <>
            <Route path={`${basePath}/`}>
            {() => (
              <AuthenticatedLayout>
                <Dashboard />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/dashboard`}>
            {() => (
              <AuthenticatedLayout>
                <Dashboard />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/records`}>
            {() => (
              <AuthenticatedLayout>
                <Records />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/analytics`}>
            {() => (
              <AuthenticatedLayout>
                <Analytics />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/family`}>
            {() => (
              <AuthenticatedLayout>
                <Family />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/appointments`}>
            {() => (
              <AuthenticatedLayout>
                <Appointments />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/assistant`}>
            {() => (
              <AuthenticatedLayout>
                <Assistant />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/emergency`}>
            {() => (
              <AuthenticatedLayout>
                <Emergency />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/profile`}>
            {() => (
              <AuthenticatedLayout>
                <Profile />
              </AuthenticatedLayout>
            )}
          </Route>
          <Route path={`${basePath}/mobile-access`}>
            {() => (
              <AuthenticatedLayout>
                <MobileAccess />
              </AuthenticatedLayout>
            )}
          </Route>
        </>
      ) : (
        <Route path="*">
          {() => {
            const redirectPath = useMockAuthSystem ? '/simple-login' : '/supabase-login';
            window.location.href = `${basePath}${redirectPath}`;
            return null;
          }}
        </Route>
      )}

      {/* Fallback 404 route */}
      <Route component={NotFound} />
    </Switch>
    </ThemeProvider>
  );
}

export default App;
