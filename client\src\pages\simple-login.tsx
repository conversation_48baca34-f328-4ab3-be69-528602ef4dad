import { useState } from "react";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MediKeyLogo } from "@/assets/icons/MediKeyLogo";
import { motion } from "framer-motion";

export default function SimpleLogin() {
  const [, navigate] = useLocation();
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("demo123");
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Simple demo login - just navigate to dashboard
    setTimeout(() => {
      setLoading(false);
      navigate("/dashboard");
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-bg-primary px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-center">
            <MediKeyLogo className="h-12 w-12 text-primary-600" />
            <h1 className="ml-3 text-3xl font-bold text-gray-800">MediKey</h1>
          </div>
          <p className="mt-2 text-gray-600">Demo Login (No Supabase Required)</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle className="text-center">Demo Access</CardTitle>
              <CardDescription className="text-center">
                Quick access to test the application
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4 border-blue-200 bg-blue-50">
                <AlertDescription className="text-blue-800">
                  This is a demo login that bypasses Supabase authentication for testing purposes.
                </AlertDescription>
              </Alert>
              
              <form onSubmit={handleLogin}>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="demo123"
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? "Signing in..." : "Demo Login"}
                  </Button>
                </div>
              </form>
              
              <div className="mt-6 space-y-3">
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">Quick Access:</p>
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate("/demo-dashboard")}
                      className="w-full"
                    >
                      🚀 Demo Dashboard
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate("/showcase")}
                      className="w-full"
                    >
                      ⭐ Project Showcase
                    </Button>
                  </div>
                </div>

                <div className="text-center pt-3 border-t">
                  <p className="text-sm text-gray-600">
                    Want to test Supabase auth?{" "}
                    <Button
                      variant="link"
                      className="p-0 h-auto"
                      onClick={() => navigate("/supabase-login")}
                    >
                      Try Supabase Login
                    </Button>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
