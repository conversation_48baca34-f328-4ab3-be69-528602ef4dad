@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Light Theme (default) */
  .light-theme {
    --background: hsl(0 0% 100%);
    --foreground: hsl(222.2 47.4% 11.2%);

    --primary: hsl(221.2 83.2% 53.3%);
    --primary-dark: hsl(221.2 83.2% 43.3%);
    --primary-light: hsl(221.2 83.2% 93.3%);
    --primary-foreground: hsl(210 40% 98%);

    --accent: hsl(217.2 91.2% 59.8%);
    --accent-foreground: hsl(222.2 47.4% 11.2%);

    --muted: hsl(210 40% 96.1%);
    --muted-foreground: hsl(215.4 16.3% 46.9%);

    --border: hsl(214.3 31.8% 91.4%);
    --input: hsl(214.3 31.8% 91.4%);

    --card: hsl(0 0% 100%);
    --card-foreground: hsl(222.2 47.4% 11.2%);

    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(210 40% 98%);

    --ring: hsl(221.2 83.2% 53.3%);

    --radius: 0.5rem;
  }

  /* Dark Theme */
  .dark-theme {
    --background: hsl(222.2 84% 4.9%);
    --foreground: hsl(210 40% 98%);

    --primary: hsl(217.2 91.2% 59.8%);
    --primary-dark: hsl(217.2 91.2% 49.8%);
    --primary-light: hsl(217.2 91.2% 20%);
    --primary-foreground: hsl(222.2 47.4% 11.2%);

    --accent: hsl(217.2 91.2% 59.8%);
    --accent-foreground: hsl(210 40% 98%);

    --muted: hsl(217.2 32.6% 17.5%);
    --muted-foreground: hsl(215 20.2% 85.1%);

    --border: hsl(217.2 32.6% 17.5%);
    --input: hsl(217.2 32.6% 17.5%);

    --card: hsl(222.2 84% 9.9%);
    --card-foreground: hsl(210 40% 98%);

    --destructive: hsl(0 62.8% 30.6%);
    --destructive-foreground: hsl(210 40% 98%);

    --ring: hsl(217.2 91.2% 59.8%);

    --radius: 0.5rem;

    /* Dark mode specific background colors */
    --dark-bg-primary: hsl(222.2 84% 4.9%);
    --dark-bg-secondary: hsl(222.2 84% 6.9%);
    --dark-bg-tertiary: hsl(222.2 84% 9.9%);
    --dark-bg-quaternary: hsl(222.2 47.4% 11.2%);
  }

  /* Default to light theme */
  :root {
    --background: hsl(0 0% 100%);
    --foreground: hsl(222.2 47.4% 11.2%);

    --primary: hsl(221.2 83.2% 53.3%);
    --primary-dark: hsl(221.2 83.2% 43.3%);
    --primary-light: hsl(221.2 83.2% 93.3%);
    --primary-foreground: hsl(210 40% 98%);

    --accent: hsl(217.2 91.2% 59.8%);
    --accent-foreground: hsl(222.2 47.4% 11.2%);

    --muted: hsl(210 40% 96.1%);
    --muted-foreground: hsl(215.4 16.3% 46.9%);

    --border: hsl(214.3 31.8% 91.4%);
    --input: hsl(214.3 31.8% 91.4%);

    --card: hsl(0 0% 100%);
    --card-foreground: hsl(222.2 47.4% 11.2%);

    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(210 40% 98%);

    --ring: hsl(221.2 83.2% 53.3%);

    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Dark mode specific styles */
  .dark-theme {
    /* Apply dark background to the entire app */
    & body,
    & #root,
    & .min-h-screen,
    & .h-screen,
    & .app-container {
      background-color: var(--dark-bg-primary);
    }

    /* Apply dark background to all common background classes */
    & .bg-white,
    & [class*="bg-gray-"],
    & .card,
    & .bg-background,
    & main,
    & aside,
    & header,
    & footer,
    & nav,
    & .dropdown-content,
    & .modal-content,
    & .popover-content,
    & .menu-content {
      background-color: var(--dark-bg-tertiary);
    }

    /* Make all text visible on dark backgrounds */
    & .text-gray-500,
    & .text-gray-600,
    & .text-gray-700,
    & .text-muted-foreground {
      color: hsl(215 20.2% 75.1%);
    }

    & .text-gray-800,
    & .text-gray-900,
    & .text-foreground,
    & h1, & h2, & h3, & h4, & h5, & h6,
    & .card-title,
    & .heading,
    & label {
      color: hsl(210 40% 98%);
    }

    /* Fix borders for dark mode */
    & .border,
    & .border-gray-100,
    & .border-gray-200,
    & .border-gray-300,
    & [class*="border-gray-"] {
      border-color: hsl(217.2 32.6% 17.5%);
    }

    /* Fix background colors */
    & .bg-gray-50 {
      background-color: var(--dark-bg-secondary);
    }

    & .bg-gray-100 {
      background-color: var(--dark-bg-tertiary);
    }

    & .bg-gray-200 {
      background-color: var(--dark-bg-quaternary);
    }

    /* Adjust shadows for dark mode */
    & .shadow,
    & .shadow-md,
    & .shadow-lg,
    & .shadow-sm,
    & .shadow-xl {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    }

    /* Fix login and register pages */
    & .min-h-screen.flex.items-center.justify-center.bg-gray-50,
    & .min-h-screen.flex.items-center.justify-center {
      background-color: var(--dark-bg-primary);
    }

    /* Fix inputs and form elements */
    & input,
    & select,
    & textarea,
    & .input,
    & .select,
    & .textarea {
      background-color: hsl(222.2 84% 4.9%);
      color: hsl(210 40% 98%);
      border-color: hsl(217.2 32.6% 17.5%);
    }

    /* Fix buttons */
    & .btn-secondary,
    & .btn-outline,
    & [class*="btn-gray"] {
      background-color: hsl(217.2 32.6% 17.5%);
      color: hsl(210 40% 98%);
    }

    /* Fix hover states */
    & .hover\:bg-gray-100:hover,
    & .hover\:bg-gray-200:hover {
      background-color: hsl(222.2 84% 14.9%) !important;
    }

    /* Fix sidebar and navigation */
    & .sidebar,
    & .nav,
    & .navbar {
      background-color: var(--card);
      border-color: hsl(217.2 32.6% 17.5%);
    }

    /* Fix cards */
    & .card {
      border: 1px solid var(--border);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    & .card-header,
    & .card-footer {
      background-color: var(--dark-bg-secondary);
      border-color: var(--border);
    }

    /* Add subtle glow to cards based on page theme */
    & .card {
      box-shadow: 0 4px 20px var(--theme-color-dark);
    }

    /* Fix tables */
    & table,
    & th,
    & td {
      border-color: hsl(217.2 32.6% 17.5%);
    }

    & th {
      background-color: hsl(222.2 84% 7.9%);
    }

    & tr:nth-child(even) {
      background-color: hsl(222.2 84% 6.9%);
    }

    /* Fix alerts and notifications */
    & .alert:not(.alert-destructive):not(.alert-warning):not(.alert-success) {
      background-color: var(--dark-bg-tertiary);
      color: hsl(210 40% 98%);
      border-color: var(--border);
    }

    /* Enhance buttons in dark mode */
    & .btn-primary,
    & button[type="submit"],
    & .bg-primary {
      box-shadow: 0 0 10px var(--theme-color);
    }

    /* Add subtle glow to active navigation items */
    & .sidebar-item.active,
    & .nav-item.active,
    & .nav-link.active,
    & a.active {
      box-shadow: inset 0 0 8px var(--theme-color);
    }

    /* Enhance scrollbars */
    & ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    & ::-webkit-scrollbar-track {
      background: var(--dark-bg-primary);
    }

    & ::-webkit-scrollbar-thumb {
      background: var(--theme-color-dark);
      border-radius: 5px;
    }

    & ::-webkit-scrollbar-thumb:hover {
      background: var(--theme-color);
    }
  }

  /* Apply theme colors to components */
  .bg-primary-100 {
    background-color: var(--primary-light);
  }

  .bg-primary-600 {
    background-color: var(--primary);
  }

  .text-primary-600 {
    color: var(--primary);
  }

  .text-primary-800 {
    color: var(--primary-dark);
  }

  .hover\:bg-primary-700:hover {
    background-color: var(--primary-dark);
  }

  .focus\:ring-primary-500:focus {
    --tw-ring-color: var(--primary);
  }

  .border-primary-500 {
    border-color: var(--primary);
  }

  /* Enhanced theme colors for more elements */

  /* Buttons */
  .btn-primary,
  .bg-primary,
  .bg-blue-600,
  .bg-blue-500 {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
  }

  /* Hover states */
  .hover\:bg-blue-700:hover,
  .hover\:bg-primary-600:hover {
    background-color: var(--primary-dark) !important;
  }

  /* Text colors */
  .text-blue-600,
  .text-blue-500 {
    color: var(--primary) !important;
  }

  /* Border colors */
  .border-blue-500,
  .border-blue-600 {
    border-color: var(--primary) !important;
  }

  /* Card headers and footers */
  .card-header,
  .card-footer {
    background-color: var(--primary-light) !important;
    border-color: var(--primary) !important;
  }

  /* Navigation active items */
  .nav-item.active,
  .nav-link.active {
    background-color: var(--primary-light) !important;
    color: var(--primary) !important;
  }

  /* Badges */
  .badge-primary {
    background-color: var(--primary) !important;
    color: white !important;
  }

  /* Progress bars */
  .progress-bar {
    background-color: var(--primary) !important;
  }

  /* Sidebar active items */
  .sidebar-item.active {
    background-color: var(--primary-light) !important;
    border-left-color: var(--primary) !important;
    color: var(--primary) !important;
  }

  /* Form focus states */
  input:focus,
  select:focus,
  textarea:focus {
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 2px var(--primary-light) !important;
  }

  /* Tabs */
  .tab.active,
  .tab[data-state="active"] {
    background-color: var(--primary-light) !important;
    color: var(--primary) !important;
    border-color: var(--primary) !important;
  }

  /* Links */
  a {
    color: var(--primary);
  }

  a:hover {
    color: var(--primary-dark);
  }
}