import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import * as schema from "@shared/schema";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import path from "path";

// Fix __dirname for ES module scope
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env from root directory
dotenv.config({ path: path.resolve(__dirname, "../.env") });

console.log("Environment:", process.env.NODE_ENV);
console.log("Database URL configured:", process.env.DATABASE_URL ? "Yes" : "No");

// Initialize Supabase PostgreSQL connection
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error("DATABASE_URL environment variable is not set. Please configure your Supabase connection string.");
}

// Configure postgres client with proper SSL settings for Supabase
const client = postgres(connectionString, {
  ssl: 'require',
  max: 20,
  idle_timeout: 20,
  connect_timeout: 10,
});

const db = drizzle(client, { schema });

console.log("Database connection established successfully using Supabase PostgreSQL");

// Simple database initialization function
export async function initializeDatabase() {
  console.log('Initializing database...');

  try {
    // Test the database connection
    await db.execute(sql`SELECT 1`);
    console.log('Supabase PostgreSQL connection verified');

    console.log('Database initialization completed successfully');
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    console.error('Please check your DATABASE_URL and ensure Supabase is accessible');
    return false;
  }
}

// Create a default admin user if no users exist
export async function createDefaultUserIfNeeded() {
  try {
    const existingUsers = await db.query.users.findMany({
      limit: 1
    });

    if (existingUsers.length === 0) {
      console.log('No users found, creating default user...');

      // Hash for password "password123"
      const hashedPassword = '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQICjYJ7ogs/D3Q1vIwLRrJfQ7mNZS';

      await db.insert(schema.users).values({
        username: 'manasvi',
        password: hashedPassword,
        fullName: 'Manasvi',
        email: '<EMAIL>',
        phone: '1234567890',
        gender: 'Male',
        bloodType: 'O+',
        createdAt: new Date(),
      });

      console.log('Default user created successfully');
    } else {
      console.log('Users already exist, skipping default user creation');
    }
  } catch (error) {
    console.error('Error creating default user:', error);
  }
}

// Export the database connection and client
export { db, client };