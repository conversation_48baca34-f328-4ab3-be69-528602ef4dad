{"id": "a29be3ec-4638-46ef-b988-776e5914439c", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ai_chat_history": {"name": "ai_chat_history", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ai_chat_history_user_id_users_id_fk": {"name": "ai_chat_history_user_id_users_id_fk", "tableFrom": "ai_chat_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "appointment_type": {"name": "appointment_type", "type": "text", "primaryKey": false, "notNull": true}, "provider_name": {"name": "provider_name", "type": "text", "primaryKey": false, "notNull": true}, "provider_type": {"name": "provider_type", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "appointment_date": {"name": "appointment_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "reminder_set": {"name": "reminder_set", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "reminder_time": {"name": "reminder_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"appointments_user_id_users_id_fk": {"name": "appointments_user_id_users_id_fk", "tableFrom": "appointments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.family_members": {"name": "family_members", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "text", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "blood_type": {"name": "blood_type", "type": "text", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "chronic_conditions": {"name": "chronic_conditions", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"family_members_user_id_users_id_fk": {"name": "family_members_user_id_users_id_fk", "tableFrom": "family_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.health_metrics": {"name": "health_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "metric_type": {"name": "metric_type", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"health_metrics_user_id_users_id_fk": {"name": "health_metrics_user_id_users_id_fk", "tableFrom": "health_metrics", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_records": {"name": "medical_records", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "record_type": {"name": "record_type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": false}, "provider_type": {"name": "provider_type", "type": "text", "primaryKey": false, "notNull": false}, "record_date": {"name": "record_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "file_content": {"name": "file_content", "type": "text", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "ai_summary": {"name": "ai_summary", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_records_user_id_users_id_fk": {"name": "medical_records_user_id_users_id_fk", "tableFrom": "medical_records", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_summary": {"name": "medical_summary", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"medical_summary_user_id_users_id_fk": {"name": "medical_summary_user_id_users_id_fk", "tableFrom": "medical_summary", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"medical_summary_user_id_unique": {"name": "medical_summary_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.smartwatch_devices": {"name": "smartwatch_devices", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "device_name": {"name": "device_name", "type": "text", "primaryKey": false, "notNull": true}, "device_id": {"name": "device_id", "type": "text", "primaryKey": false, "notNull": true}, "device_type": {"name": "device_type", "type": "text", "primaryKey": false, "notNull": true}, "connected_at": {"name": "connected_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "last_sync": {"name": "last_sync", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}}, "indexes": {}, "foreignKeys": {"smartwatch_devices_user_id_users_id_fk": {"name": "smartwatch_devices_user_id_users_id_fk", "tableFrom": "smartwatch_devices", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "blood_type": {"name": "blood_type", "type": "text", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "text", "primaryKey": false, "notNull": false}, "chronic_conditions": {"name": "chronic_conditions", "type": "text", "primaryKey": false, "notNull": false}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "text", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "text", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}