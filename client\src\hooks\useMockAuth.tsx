import { createContext, useContext, useState, useEffect } from "react";
import { useLocation } from "wouter";

interface MockUser {
  id: string;
  email: string;
  user_metadata?: {
    full_name?: string;
  };
}

interface MockAuthContextType {
  user: MockUser | null;
  session: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  signUp: (email: string, password: string, userData?: any) => Promise<{ data: any; error: any }>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ data: any; error: any }>;
  updatePassword: (password: string) => Promise<{ data: any; error: any }>;
}

const MockAuthContext = createContext<MockAuthContextType>({
  user: null,
  session: null,
  isAuthenticated: false,
  isLoading: false,
  signUp: async () => ({ data: null, error: null }),
  signIn: async () => ({ data: null, error: null }),
  signOut: async () => {},
  resetPassword: async () => ({ data: null, error: null }),
  updatePassword: async () => ({ data: null, error: null }),
});

export function MockAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<MockUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [, navigate] = useLocation();

  // Check for existing session on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('mockUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  const signUp = async (email: string, password: string, userData?: any) => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newUser: MockUser = {
      id: Date.now().toString(),
      email,
      user_metadata: {
        full_name: userData?.full_name || 'Demo User'
      }
    };
    
    setUser(newUser);
    localStorage.setItem('mockUser', JSON.stringify(newUser));
    setIsLoading(false);
    
    return { 
      data: { user: newUser }, 
      error: null 
    };
  };

  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser: MockUser = {
      id: '1',
      email,
      user_metadata: {
        full_name: 'Demo User'
      }
    };
    
    setUser(mockUser);
    localStorage.setItem('mockUser', JSON.stringify(mockUser));
    setIsLoading(false);
    
    return { 
      data: { user: mockUser }, 
      error: null 
    };
  };

  const signOut = async () => {
    setUser(null);
    localStorage.removeItem('mockUser');
    navigate('/simple-login');
  };

  const resetPassword = async (email: string) => {
    // Mock password reset
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { 
      data: { message: 'Password reset email sent' }, 
      error: null 
    };
  };

  const updatePassword = async (password: string) => {
    // Mock password update
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { 
      data: { message: 'Password updated' }, 
      error: null 
    };
  };

  const contextValue = {
    user,
    session: user ? { user } : null,
    isAuthenticated: !!user,
    isLoading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
  };

  return (
    <MockAuthContext.Provider value={contextValue}>
      {children}
    </MockAuthContext.Provider>
  );
}

export const useMockAuth = () => useContext(MockAuthContext);
