const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Enable CORS
app.use(cors());

// Parse JSON
app.use(express.json());

// Serve static files from dist/public
app.use(express.static(path.join(__dirname, 'dist', 'public')));

// Simple API routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running!' });
});

// Mock login endpoint
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  if (username === 'manasvi' && password === 'password123') {
    res.json({ 
      success: true, 
      user: { 
        id: 1, 
        username: 'manasvi', 
        fullName: 'Manasvi',
        email: '<EMAIL>'
      } 
    });
  } else {
    res.status(401).json({ success: false, message: 'Invalid credentials' });
  }
});

// Mock user endpoint
app.get('/api/auth/me', (req, res) => {
  res.json({ 
    id: 1, 
    username: 'manasvi', 
    fullName: 'Manasvi',
    email: '<EMAIL>'
  });
});

// Mock health metrics
app.get('/api/health-metrics', (req, res) => {
  res.json({
    bloodPressure: {
      latest: "120/80",
      trend: "stable",
      chartData: [
        { date: "2024-01-01", systolic: 120, diastolic: 80 },
        { date: "2024-01-02", systolic: 118, diastolic: 78 },
        { date: "2024-01-03", systolic: 122, diastolic: 82 },
        { date: "2024-01-04", systolic: 119, diastolic: 79 },
        { date: "2024-01-05", systolic: 121, diastolic: 81 }
      ]
    },
    bloodSugar: {
      latest: "95 mg/dL",
      trend: "normal",
      chartData: [
        { date: "2024-01-01", value: 95 },
        { date: "2024-01-02", value: 92 },
        { date: "2024-01-03", value: 98 },
        { date: "2024-01-04", value: 94 },
        { date: "2024-01-05", value: 96 }
      ]
    },
    weight: {
      latest: "70 kg",
      trend: "stable",
      chartData: [
        { date: "2024-01-01", value: 70.2 },
        { date: "2024-01-02", value: 70.0 },
        { date: "2024-01-03", value: 70.1 },
        { date: "2024-01-04", value: 69.9 },
        { date: "2024-01-05", value: 70.0 }
      ]
    },
    heartRate: {
      latest: "72 bpm",
      trend: "normal",
      chartData: [
        { date: "2024-01-01", value: 72 },
        { date: "2024-01-02", value: 70 },
        { date: "2024-01-03", value: 74 },
        { date: "2024-01-04", value: 71 },
        { date: "2024-01-05", value: 73 }
      ]
    }
  });
});

// Catch all handler: send back React's index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client', 'dist', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📊 API available at http://localhost:${PORT}/api/health`);
  console.log(`🔐 Login with username: manasvi, password: password123`);
});
