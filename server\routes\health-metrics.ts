import { Router } from "express";
import { db } from "../db";
import * as schema from "@shared/schema";
import { eq, desc } from "drizzle-orm";

const router = Router();

// Mock data generator for health metrics
function generateMockHealthData() {
  const now = new Date();
  const data = [];
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      systolic: 120 + Math.floor(Math.random() * 20) - 10,
      diastolic: 80 + Math.floor(Math.random() * 15) - 7,
      bloodSugar: 90 + Math.floor(Math.random() * 30),
      weight: 165 + Math.floor(Math.random() * 10) - 5,
      bmi: 24.5 + Math.floor(Math.random() * 4) - 2
    });
  }
  
  return data;
}

// GET /api/health-metrics
router.get("/", async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    // Try to get real data from database
    const realMetrics = await db.query.healthMetrics.findMany({
      where: eq(schema.healthMetrics.userId, userId),
      orderBy: desc(schema.healthMetrics.recordedAt),
      limit: 30
    });

    // If no real data, use mock data
    const mockData = generateMockHealthData();
    
    const response = {
      bloodPressure: {
        latest: "128/82 mmHg",
        change: -4,
        data: mockData.map(d => ({
          date: d.date,
          systolic: d.systolic,
          diastolic: d.diastolic
        }))
      },
      bloodSugar: {
        latest: "105 mg/dL",
        change: -2,
        data: mockData.map(d => ({
          date: d.date,
          value: d.bloodSugar
        }))
      },
      weight: {
        latest: "165 lbs / 24.2 BMI",
        change: -1.3,
        data: mockData.map(d => ({
          date: d.date,
          value: d.weight,
          bmi: d.bmi
        }))
      }
    };

    res.json(response);
  } catch (error) {
    console.error("Error fetching health metrics:", error);
    res.status(500).json({ error: "Failed to fetch health metrics" });
  }
});

// POST /api/health-metrics
router.post("/", async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: "Unauthorized" });
    }

    const { metricType, value, unit, recordedAt, notes } = req.body;

    const newMetric = await db.insert(schema.healthMetrics).values({
      userId,
      metricType,
      value: JSON.stringify(value),
      unit,
      recordedAt: new Date(recordedAt),
      notes
    }).returning();

    res.status(201).json(newMetric[0]);
  } catch (error) {
    console.error("Error creating health metric:", error);
    res.status(500).json({ error: "Failed to create health metric" });
  }
});

export default router;
