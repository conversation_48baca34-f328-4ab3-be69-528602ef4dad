import { drizzle } from 'drizzle-orm/postgres-js';
import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import * as schema from "../shared/schema";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import path from "path";

// Fix __dirname for ES module scope
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env from root directory
dotenv.config({ path: path.resolve(__dirname, "../.env") });

async function setupDatabase() {
  console.log('Setting up Supabase database...');
  
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    throw new Error("DATABASE_URL environment variable is not set");
  }

  // Configure postgres client with proper SSL settings for Supabase
  const client = postgres(connectionString, {
    ssl: 'require',
    max: 20,
    idle_timeout: 20,
    connect_timeout: 10,
  });

  const db = drizzle(client, { schema });

  try {
    console.log('Testing database connection...');
    await db.execute(sql`SELECT 1`);
    console.log('✓ Database connection successful');

    // Create tables using raw SQL to avoid drizzle-kit interactive prompts
    console.log('Creating tables...');
    
    // Drop existing tables if they exist (be careful in production!)
    await db.execute(sql`DROP TABLE IF EXISTS ai_chat_history CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS smartwatch_devices CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS medical_summary CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS health_metrics CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS family_members CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS appointments CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS medical_records CASCADE`);
    await db.execute(sql`DROP TABLE IF EXISTS users CASCADE`);

    // Create users table
    await db.execute(sql`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        phone TEXT,
        date_of_birth TEXT,
        gender TEXT,
        blood_type TEXT,
        allergies TEXT,
        chronic_conditions TEXT,
        emergency_contact_name TEXT,
        emergency_contact_phone TEXT,
        avatar_url TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Users table created');

    // Create medical_records table
    await db.execute(sql`
      CREATE TABLE medical_records (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        title TEXT NOT NULL,
        description TEXT,
        record_type TEXT NOT NULL,
        provider TEXT,
        provider_type TEXT,
        record_date TIMESTAMP NOT NULL,
        file_content TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_name TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        tags TEXT,
        ai_summary TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Medical records table created');

    // Create health_metrics table
    await db.execute(sql`
      CREATE TABLE health_metrics (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        metric_type TEXT NOT NULL,
        value TEXT NOT NULL,
        unit TEXT NOT NULL,
        recorded_at TIMESTAMP NOT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Health metrics table created');

    // Create appointments table
    await db.execute(sql`
      CREATE TABLE appointments (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        title TEXT NOT NULL,
        description TEXT,
        appointment_type TEXT NOT NULL,
        provider_name TEXT NOT NULL,
        provider_type TEXT,
        location TEXT,
        appointment_date TIMESTAMP NOT NULL,
        duration INTEGER,
        reminder_set BOOLEAN DEFAULT FALSE,
        reminder_time TIMESTAMP,
        status TEXT DEFAULT 'scheduled',
        notes TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Appointments table created');

    // Create family_members table
    await db.execute(sql`
      CREATE TABLE family_members (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        name TEXT NOT NULL,
        relationship TEXT NOT NULL,
        date_of_birth TEXT,
        gender TEXT,
        blood_type TEXT,
        allergies TEXT,
        chronic_conditions TEXT,
        avatar_url TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Family members table created');

    // Create ai_chat_history table
    await db.execute(sql`
      CREATE TABLE ai_chat_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        message TEXT NOT NULL,
        response TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ AI chat history table created');

    // Create smartwatch_devices table
    await db.execute(sql`
      CREATE TABLE smartwatch_devices (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id),
        device_name TEXT NOT NULL,
        device_id TEXT NOT NULL,
        device_type TEXT NOT NULL,
        connected_at TIMESTAMP DEFAULT NOW(),
        last_sync TIMESTAMP,
        status TEXT DEFAULT 'active'
      )
    `);
    console.log('✓ Smartwatch devices table created');

    // Create medical_summary table
    await db.execute(sql`
      CREATE TABLE medical_summary (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL UNIQUE REFERENCES users(id),
        summary TEXT,
        last_updated TIMESTAMP DEFAULT NOW()
      )
    `);
    console.log('✓ Medical summary table created');

    console.log('✅ All tables created successfully!');

    // Create default user
    console.log('Creating default user...');
    const hashedPassword = '$2b$10$3euPcmQFCiblsZeEu5s7p.9MQICjYJ7ogs/D3Q1vIwLRrJfQ7mNZS'; // password123
    
    await db.execute(sql`
      INSERT INTO users (username, password, full_name, email, phone, gender, blood_type)
      VALUES ('manasvi', ${hashedPassword}, 'Manasvi', '<EMAIL>', '**********', 'Male', 'O+')
    `);
    console.log('✓ Default user created (username: manasvi, password: password123)');

    console.log('🎉 Database setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up database:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the setup
setupDatabase().catch(console.error);
