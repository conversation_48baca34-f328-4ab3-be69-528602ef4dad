import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/lib/supabase";

export default function SupabaseTest() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("testpassword123");

  const addResult = (test: string, result: any, error?: any) => {
    setTestResults(prev => [...prev, {
      test,
      result: error ? `ERROR: ${error.message}` : JSON.stringify(result, null, 2),
      success: !error,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testConnection = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.from('users').select('count').limit(1);
      addResult("Database Connection", data, error);
    } catch (error) {
      addResult("Database Connection", null, error);
    }
    setLoading(false);
  };

  const testAuth = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.getSession();
      addResult("Get Session", data, error);
    } catch (error) {
      addResult("Get Session", null, error);
    }
    setLoading(false);
  };

  const testSignUp = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: "Test User"
          }
        }
      });
      addResult("Sign Up", data, error);
    } catch (error) {
      addResult("Sign Up", null, error);
    }
    setLoading(false);
  };

  const testSignIn = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      addResult("Sign In", data, error);
    } catch (error) {
      addResult("Sign In", null, error);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Supabase Connection Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Test Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="password">Test Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex gap-4 flex-wrap">
              <Button onClick={testConnection} disabled={loading}>
                Test Database Connection
              </Button>
              <Button onClick={testAuth} disabled={loading}>
                Test Auth Session
              </Button>
              <Button onClick={testSignUp} disabled={loading}>
                Test Sign Up
              </Button>
              <Button onClick={testSignIn} disabled={loading}>
                Test Sign In
              </Button>
              <Button onClick={clearResults} variant="outline">
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500">No tests run yet. Click a test button above.</p>
              ) : (
                testResults.map((result, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded border ${
                      result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold">{result.test}</h4>
                      <span className="text-sm text-gray-500">{result.timestamp}</span>
                    </div>
                    <pre className="text-sm overflow-auto bg-white p-2 rounded border">
                      {result.result}
                    </pre>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Configuration Check</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Supabase URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</p>
              <p><strong>Supabase Anon Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</p>
              <p><strong>Key Length:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY?.length || 0} characters</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
