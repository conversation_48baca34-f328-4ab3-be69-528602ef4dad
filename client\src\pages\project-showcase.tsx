import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { 
  Heart, Activity, Users, Calendar, FileText, Brain, 
  Smartphone, Shield, Database, Cloud, Zap, Target,
  CheckCircle, Star, TrendingUp, Award, Globe, Lock
} from "lucide-react";

const features = [
  {
    icon: Heart,
    title: "Health Monitoring",
    description: "Real-time tracking of vital signs, blood pressure, blood sugar, and weight with interactive charts",
    status: "✅ Complete"
  },
  {
    icon: Activity,
    title: "SmartWatch Integration",
    description: "Seamless connection with fitness trackers for steps, heart rate, and activity monitoring",
    status: "✅ Complete"
  },
  {
    icon: FileText,
    title: "Medical Records",
    description: "Secure storage and AI-powered analysis of medical documents with smart categorization",
    status: "✅ Complete"
  },
  {
    icon: Calendar,
    title: "Appointment Management",
    description: "Schedule, track, and manage medical appointments with provider information",
    status: "✅ Complete"
  },
  {
    icon: Users,
    title: "Family Vault",
    description: "Manage health records for family members with role-based access control",
    status: "✅ Complete"
  },
  {
    icon: Brain,
    title: "AI Health Assistant",
    description: "Intelligent insights, medication reminders, and personalized health recommendations",
    status: "✅ Complete"
  },
  {
    icon: Shield,
    title: "Emergency Access",
    description: "Quick access to critical health information during emergencies",
    status: "✅ Complete"
  },
  {
    icon: Smartphone,
    title: "Mobile Responsive",
    description: "Fully responsive design that works perfectly on all devices",
    status: "✅ Complete"
  }
];

const techStack = [
  { name: "React 18", category: "Frontend", color: "bg-blue-500" },
  { name: "TypeScript", category: "Language", color: "bg-blue-600" },
  { name: "Tailwind CSS", category: "Styling", color: "bg-cyan-500" },
  { name: "Framer Motion", category: "Animation", color: "bg-purple-500" },
  { name: "Recharts", category: "Visualization", color: "bg-green-500" },
  { name: "Node.js", category: "Backend", color: "bg-green-600" },
  { name: "Express", category: "API", color: "bg-gray-600" },
  { name: "Supabase", category: "Database", color: "bg-emerald-500" },
  { name: "PostgreSQL", category: "Database", color: "bg-blue-700" },
  { name: "OpenAI", category: "AI", color: "bg-black" },
  { name: "Vite", category: "Build Tool", color: "bg-yellow-500" },
  { name: "Wouter", category: "Routing", color: "bg-red-500" }
];

const stats = [
  { label: "Components", value: "50+", icon: Target },
  { label: "Pages", value: "12", icon: FileText },
  { label: "API Endpoints", value: "25+", icon: Database },
  { label: "Features", value: "8", icon: Star },
  { label: "Responsive", value: "100%", icon: Smartphone },
  { label: "Security", value: "Enterprise", icon: Lock }
];

export default function ProjectShowcase() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-6">
            <Heart className="w-12 h-12 text-red-500 mr-4" />
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              MediKey
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-4">
            Universal Digital Medical Vault
          </p>
          <p className="text-lg text-gray-500 dark:text-gray-400 max-w-3xl mx-auto">
            A comprehensive healthcare management platform that securely stores, organizes, and analyzes your medical data 
            with AI-powered insights and seamless device integration.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
              <Globe className="w-5 h-5 mr-2" />
              Live Demo
            </Button>
            <Button size="lg" variant="outline">
              <FileText className="w-5 h-5 mr-2" />
              Documentation
            </Button>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-16"
        >
          {stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="p-4">
                <stat.icon className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                <div className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            Complete Feature Set
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <feature.icon className="w-8 h-8 text-blue-500" />
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Done
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Tech Stack */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">
            Modern Tech Stack
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {techStack.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.05 * index }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4 text-center">
                    <div className={`w-3 h-3 rounded-full ${tech.color} mx-auto mb-2`}></div>
                    <div className="font-semibold text-gray-900 dark:text-white">{tech.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{tech.category}</div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Key Highlights */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <CardHeader>
              <TrendingUp className="w-8 h-8 mb-2" />
              <CardTitle>Real-time Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Interactive charts and visualizations for health metrics with trend analysis and AI-powered insights.</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white">
            <CardHeader>
              <Shield className="w-8 h-8 mb-2" />
              <CardTitle>Enterprise Security</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Bank-level encryption, secure authentication, and HIPAA-compliant data handling for maximum privacy.</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white">
            <CardHeader>
              <Brain className="w-8 h-8 mb-2" />
              <CardTitle>AI-Powered</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Machine learning algorithms provide personalized health insights and predictive analytics.</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="text-center bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl p-12 text-white"
        >
          <Award className="w-16 h-16 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">Ready to Experience MediKey?</h2>
          <p className="text-xl mb-8 opacity-90">
            Explore the full-featured demo with real data visualizations and interactive components.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
              <Zap className="w-5 h-5 mr-2" />
              Try Demo Dashboard
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Heart className="w-5 h-5 mr-2" />
              View Health Analytics
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
