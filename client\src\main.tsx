import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { MockAuthProvider } from "@/hooks/useMockAuth";

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <MockAuthProvider>
      <App />
      <Toaster />
    </MockAuthProvider>
  </QueryClientProvider>
);
