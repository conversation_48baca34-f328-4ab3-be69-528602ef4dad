// Comprehensive demo data for MediKey application

export const demoUser = {
  id: "1",
  email: "<EMAIL>",
  fullName: "Dr. <PERSON>",
  dateOfBirth: "1985-03-15",
  gender: "Female",
  bloodType: "O+",
  height: "5'6\"",
  weight: "140 lbs",
  allergies: ["Penicillin", "Shellfish"],
  chronicConditions: ["Hypertension", "Type 2 Diabetes"],
  emergencyContactName: "<PERSON>",
  emergencyContactPhone: "+****************",
  profilePicture: null,
  createdAt: "2024-01-15T00:00:00Z"
};

export const demoHealthMetrics = {
  bloodPressure: {
    latest: "128/82 mmHg",
    change: -4,
    trend: "improving",
    data: Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split('T')[0],
        systolic: 120 + Math.floor(Math.random() * 20) - 10,
        diastolic: 80 + Math.floor(Math.random() * 15) - 7
      };
    })
  },
  bloodSugar: {
    latest: "105 mg/dL",
    change: -2,
    trend: "stable",
    data: Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return {
        date: date.toISOString().split('T')[0],
        value: 90 + Math.floor(Math.random() * 30)
      };
    })
  },
  weight: {
    latest: "140 lbs / 22.4 BMI",
    change: -1.3,
    trend: "decreasing",
    data: Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      const weight = 140 - (i * 0.1);
      return {
        date: date.toISOString().split('T')[0],
        value: parseFloat(weight.toFixed(1)),
        bmi: parseFloat((weight / (66 * 66) * 703).toFixed(1))
      };
    })
  },
  heartRate: {
    latest: "72 bpm",
    change: 2,
    trend: "normal",
    data: Array.from({ length: 24 }, (_, i) => ({
      time: `${i.toString().padStart(2, '0')}:00`,
      value: 65 + Math.floor(Math.random() * 20)
    }))
  }
};

export const demoMedicalRecords = [
  {
    id: 1,
    title: "Annual Physical Examination",
    description: "Comprehensive health checkup with blood work and vital signs assessment",
    recordType: "checkup",
    provider: "Dr. Michael Chen",
    providerType: "Primary Care Physician",
    recordDate: "2024-03-01",
    tags: ["annual", "physical", "blood work"],
    aiSummary: "Patient shows excellent overall health. Blood pressure slightly elevated but within normal range. Recommended continued monitoring of cholesterol levels.",
    fileType: "application/pdf",
    fileName: "annual_physical_2024.pdf"
  },
  {
    id: 2,
    title: "Cardiology Consultation",
    description: "Follow-up consultation for hypertension management",
    recordType: "consultation",
    provider: "Dr. Emily Rodriguez",
    providerType: "Cardiologist",
    recordDate: "2024-02-15",
    tags: ["cardiology", "hypertension", "follow-up"],
    aiSummary: "Blood pressure well controlled with current medication. Patient advised to continue current regimen and lifestyle modifications.",
    fileType: "application/pdf",
    fileName: "cardiology_consultation.pdf"
  },
  {
    id: 3,
    title: "Laboratory Results - Lipid Panel",
    description: "Comprehensive metabolic panel and lipid analysis",
    recordType: "lab",
    provider: "Quest Diagnostics",
    providerType: "Laboratory",
    recordDate: "2024-02-28",
    tags: ["lab", "lipid", "cholesterol"],
    aiSummary: "Cholesterol levels improved since last test. HDL within normal range, LDL slightly elevated but trending downward.",
    fileType: "application/pdf",
    fileName: "lipid_panel_results.pdf"
  }
];

export const demoAppointments = [
  {
    id: 1,
    title: "Quarterly Diabetes Check",
    provider: "Dr. Sarah Williams",
    providerType: "Endocrinologist",
    appointmentDate: "2024-04-15T10:00:00Z",
    duration: 60,
    location: "Medical Center - Building A, Suite 301",
    notes: "Bring current glucose meter and medication list",
    status: "confirmed",
    type: "follow-up"
  },
  {
    id: 2,
    title: "Dental Cleaning",
    provider: "Dr. James Park",
    providerType: "Dentist",
    appointmentDate: "2024-04-08T14:30:00Z",
    duration: 45,
    location: "Smile Dental Care - 123 Main St",
    notes: "Regular cleaning and checkup",
    status: "confirmed",
    type: "routine"
  },
  {
    id: 3,
    title: "Eye Examination",
    provider: "Dr. Lisa Thompson",
    providerType: "Ophthalmologist",
    appointmentDate: "2024-04-22T09:15:00Z",
    duration: 30,
    location: "Vision Center - 456 Oak Ave",
    notes: "Annual eye exam, bring current glasses",
    status: "pending",
    type: "routine"
  }
];

export const demoFamilyMembers = [
  {
    id: 1,
    name: "John Johnson",
    relationship: "Spouse",
    dateOfBirth: "1982-07-20",
    bloodType: "A+",
    allergies: ["Latex"],
    chronicConditions: [],
    emergencyContact: true,
    phone: "+****************"
  },
  {
    id: 2,
    name: "Emma Johnson",
    relationship: "Daughter",
    dateOfBirth: "2010-11-03",
    bloodType: "O+",
    allergies: ["Peanuts"],
    chronicConditions: ["Asthma"],
    emergencyContact: false,
    phone: null
  },
  {
    id: 3,
    name: "Robert Johnson Sr.",
    relationship: "Father",
    dateOfBirth: "1955-12-10",
    bloodType: "O+",
    allergies: [],
    chronicConditions: ["Diabetes", "Heart Disease"],
    emergencyContact: false,
    phone: "+****************"
  }
];

export const demoSmartWatchData = {
  steps: {
    today: 8547,
    goal: 10000,
    data: Array.from({ length: 7 }, (_, i) => ({
      day: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { weekday: 'short' }),
      steps: 7000 + Math.floor(Math.random() * 4000)
    }))
  },
  heartRate: {
    current: 72,
    resting: 65,
    max: 185,
    zones: {
      fat_burn: 45,
      cardio: 30,
      peak: 15,
      rest: 10
    }
  },
  sleep: {
    lastNight: {
      duration: 7.5,
      quality: 85,
      deep: 1.8,
      rem: 1.2,
      light: 4.5
    },
    weekAverage: 7.2
  },
  activity: {
    calories: 2150,
    activeMinutes: 45,
    distance: 4.2
  }
};

export const demoAIInsights = [
  {
    id: 1,
    type: "health_trend",
    title: "Blood Pressure Improvement",
    message: "Your blood pressure has shown consistent improvement over the past month. Keep up the great work with your medication and lifestyle changes!",
    priority: "positive",
    date: "2024-03-15"
  },
  {
    id: 2,
    type: "medication_reminder",
    title: "Medication Refill Due",
    message: "Your Lisinopril prescription will need a refill in 5 days. Consider scheduling a pharmacy pickup or delivery.",
    priority: "medium",
    date: "2024-03-14"
  },
  {
    id: 3,
    type: "appointment_suggestion",
    title: "Annual Eye Exam Due",
    message: "It's been over a year since your last eye examination. Consider scheduling an appointment with your ophthalmologist.",
    priority: "low",
    date: "2024-03-13"
  }
];

export const demoAnalytics = {
  healthScore: 87,
  trends: {
    improving: ["Blood Pressure", "Weight", "Sleep Quality"],
    stable: ["Blood Sugar", "Heart Rate"],
    needsAttention: ["Cholesterol", "Exercise Frequency"]
  },
  goals: {
    completed: 12,
    inProgress: 5,
    total: 20
  },
  streaks: {
    medication: 45,
    exercise: 12,
    sleep: 8
  }
};
